<!DOCTYPE html>
<html>
<head>
    <title>Detailed Vue Debug</title>
</head>
<body>
    <h1>Detailed Vue Debug</h1>
    <div id="log"></div>
    
    <script>
        function log(msg) {
            console.log(msg);
            document.getElementById('log').innerHTML += '<p>' + msg + '</p>';
        }
        
        log('Starting debug...');
        log('Before loading Vue script');
        log('typeof Vue: ' + typeof Vue);
        log('typeof window.Vue: ' + typeof window.Vue);
        
        // 手动创建script标签
        const script = document.createElement('script');
        script.src = 'js/vue.global.js';
        
        script.onload = function() {
            log('Vue script loaded successfully');
            log('typeof Vue after load: ' + typeof Vue);
            log('typeof window.Vue after load: ' + typeof window.Vue);
            
            // 检查全局对象
            const vueKeys = Object.keys(window).filter(key => key.toLowerCase().includes('vue'));
            log('Vue-related keys in window: ' + JSON.stringify(vueKeys));
            
            if (typeof Vue !== 'undefined') {
                log('Vue object exists!');
                log('Vue.version: ' + Vue.version);
                log('Vue.createApp: ' + typeof Vue.createApp);
                
                // 尝试创建Vue应用
                try {
                    const app = Vue.createApp({
                        data() {
                            return { message: 'Vue works!' };
                        }
                    });
                    log('Vue app created successfully');
                } catch (e) {
                    log('Error creating Vue app: ' + e.message);
                }
            } else {
                log('Vue object does not exist after script load');
            }
        };
        
        script.onerror = function() {
            log('Error loading Vue script');
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
