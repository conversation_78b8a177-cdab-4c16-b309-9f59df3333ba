# 心动告白 - Heart Confession App

一个基于Vue.js 3开发的浪漫告白移动端响应式网页应用。

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start-server.bat` 文件
2. 等待服务器启动
3. 在浏览器中访问 `http://localhost:8009`

### 方法二：手动启动
1. 打开命令行，进入项目目录
2. 运行：`python -m http.server 8009`
3. 在浏览器中访问 `http://localhost:8009`

### 方法三：直接打开（功能受限）
- 双击 `index-no-vue.html` 文件（原生JS版本，功能相对完整）
- 双击 `index.html` 文件（Vue.js版本，功能受限）

> ⚠️ 注意：直接双击HTML文件在某些浏览器中可能功能受限，建议使用本地服务器方式访问以获得最佳体验。

## 🔗 访问链接

- **主应用（Vue.js版本）**：http://localhost:8009
- **原生JS版本**：http://localhost:8009/index-no-vue.html
- **测试页面**：http://localhost:8009/test.html
- **Vue.js诊断工具**：http://localhost:8009/vue-check.html
- **通用诊断工具**：http://localhost:8009/diagnose.html
- **Vue.js最小化测试**：http://localhost:8009/vue-test.html

## 📁 文件结构

```
C:\Users\<USER>\Documents\augment-projects\123\
├── index.html              # Vue.js版本主应用页面
├── index-no-vue.html       # 原生JS版本主应用页面
├── vue-check.html          # Vue.js加载诊断工具
├── diagnose.html           # 通用诊断工具
├── vue-test.html           # Vue.js最小化测试
├── test.html               # 测试页面
├── start-server.bat        # 启动脚本
├── manifest.json           # PWA清单
├── sw.js                   # Service Worker
├── css\
│   └── style.css           # 样式文件
├── js\
│   ├── vue-app.js          # Vue.js 3应用
│   └── vue.global.js       # Vue.js库文件（本地）
├── assets\
│   └── audio\
│       └── romantic-musi.mp3  # 背景音乐文件
└── README.md               # 说明文档
```

## ✨ 功能特性

- **心形点击动画** - 炫酷的爆炸和粒子效果
- **递进式告白** - 6句精心设计的告白语句
- **背景音乐** - 浪漫的背景音乐营造氛围
- **自定义告白** - 可以修改告白内容
- **响应式设计** - 完美适配移动设备
- **PWA支持** - 可安装到桌面
- **分享功能** - 一键分享给朋友

## 🎵 音乐功能

### 音乐文件
- 当前使用：`assets/audio/romantic-musi.mp3`
- 支持格式：MP3, WAV, OGG等浏览器支持的音频格式
- 音量设置：自动调节为30%，避免过大声音

### 播放控制
- **自动播放**：首次点击心形时自动尝试播放
- **手动控制**：点击音乐按钮（🎵）开启/关闭
- **循环播放**：音乐会自动循环播放
- **状态指示**：按钮会显示当前播放状态

### 更换音乐
如需更换背景音乐：
1. 将新的音频文件放入 `assets/audio/` 文件夹
2. 重命名为 `romantic-musi.mp3` 或修改HTML中的文件路径
3. 刷新页面即可使用新音乐

## 🎮 使用说明

1. **开始告白** - 点击中央的心形图案
2. **查看消息** - 每次点击显示一句告白
3. **背景音乐** - 点击音乐按钮开启/关闭背景音乐
   - 首次点击心形时会自动尝试播放音乐
   - 如果自动播放失败，请手动点击音乐按钮
4. **自定义** - 点击右上角设置按钮修改告白内容
5. **分享** - 点击分享按钮分享给朋友

## 🔧 技术栈

- Vue.js 3 (Composition API) - 主要版本
- 原生JavaScript (备用)
- HTML5 Canvas
- CSS3 动画
- 响应式设计
- PWA技术

## 🧪 测试

项目包含多个测试页面，可以用于验证不同功能：

1. **test.html** - Vue.js、原生JS和PWA功能测试
2. **vue-check.html** - Vue.js加载问题详细诊断
3. **diagnose.html** - 通用诊断工具（不依赖Vue.js）
4. **vue-test.html** - Vue.js最小化功能测试

运行测试：
1. 启动本地服务器
2. 访问对应的测试页面URL

## 🔍 故障排除

### 无法通过HTTP服务器访问？
如果无法通过HTTP服务器访问（http://localhost:8009），您可以：

1. 确保已安装Python 3.x
2. 双击 `start-server.bat` 文件启动服务器
3. 如果仍然无法访问，请尝试手动启动：
   - Windows: 打开命令提示符，运行 `cd /d e:\123 && python -m http.server 8009`
   - Mac/Linux: 打开终端，运行 `cd e:\123 && python3 -m http.server 8009`

### Vue.js版本无法加载？
如果您遇到Vue.js版本加载失败的问题，请按以下步骤排查：

1. 使用诊断工具：
   - 访问 http://localhost:8009/diagnose.html 进行通用诊断
   - 访问 http://localhost:8009/vue-check.html 进行Vue.js详细诊断
   - 访问 http://localhost:8009/vue-test.html 进行Vue.js最小化测试

2. 常见问题及解决方案：
   - **访问方式错误**：确保通过HTTP服务器访问，而不是直接打开HTML文件
   - **文件缺失**：检查 js/vue.global.js 和 js/vue-app.js 是否存在
   - **文件损坏**：尝试重新下载Vue.js文件
   - **浏览器缓存**：清除浏览器缓存后重试

### 直接打开HTML文件功能受限？
这是正常现象，因为：
1. 浏览器安全策略阻止本地文件访问
2. Service Worker和PWA功能需要HTTPS或localhost环境
3. XMLHttpRequest在file://协议下无法正常工作

### 音乐无法播放怎么办?
1. 点击音乐按钮手动播放
2. 检查浏览器是否阻止了自动播放
3. 确认音频文件是否存在
4. 尝试刷新页面

### PWA功能无法使用怎么办?
1. 确保通过HTTP服务器访问（而非直接打开文件）
2. 检查浏览器是否支持PWA功能
3. 查看浏览器控制台是否有错误信息

## 📱 兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- 移动端浏览器

## ❗ 常见问题解答

### 为什么提供两个版本?
由于网络环境或CDN问题，Vue.js版本可能无法正常加载。为确保所有功能正常运行，我们提供了原生JavaScript版本，该版本不依赖任何外部库，功能完整且稳定。

### Vue.js版本加载失败怎么办?
如果Vue.js版本加载失败，您可以：
1. 使用诊断工具（diagnose.html 或 vue-check.html）排查具体问题
2. 直接访问 `index-no-vue.html` 使用原生JS版本
3. 确保通过HTTP服务器访问而不是直接打开文件

## 💝 告白语句

默认包含6句递进式告白：
1. "你知道吗？你的笑容是我见过最美的风景。"
2. "每当我看到你，我的心跳都会不由自主地加速。"
3. "和你在一起的每一刻，都让我感到无比幸福。"
4. "你的温柔如春风，轻抚着我的心田。"
5. "我想牵着你的手，走过人生的每一个春夏秋冬。"
6. "我爱你，不只是今天，而是每一个明天。"

---

💝 **用这个浪漫的应用表达你的心意吧！** 💝
