<!DOCTYPE html>
<html>
<head>
    <title>Fix Vue Test</title>
</head>
<body>
    <h1>Fix Vue Test</h1>
    <div id="log"></div>
    <div id="app">{{ message }}</div>
    
    <script>
        function log(msg) {
            console.log(msg);
            document.getElementById('log').innerHTML += '<p>' + msg + '</p>';
        }
        
        log('Loading Vue...');
        
        // 创建script标签加载Vue
        const script = document.createElement('script');
        script.src = 'js/vue.global.js';
        
        script.onload = function() {
            log('Vue script loaded');
            log('typeof Vue: ' + typeof Vue);
            
            // 如果Vue没有正确暴露，尝试从全局作用域获取
            if (typeof Vue === 'undefined') {
                log('Vue not found in global scope, checking window...');
                
                // 检查是否有其他Vue相关的全局变量
                const vueKeys = Object.keys(window).filter(key => 
                    key.toLowerCase().includes('vue') || 
                    (window[key] && typeof window[key] === 'object' && window[key].createApp)
                );
                log('Vue-related keys: ' + JSON.stringify(vueKeys));
                
                // 尝试手动执行Vue.js文件内容
                try {
                    // 重新执行Vue.js文件，确保Vue对象被正确创建
                    eval(`
                        if (typeof Vue === 'undefined') {
                            // 手动创建Vue对象引用
                            window.Vue = (function() {
                                // 重新加载Vue.js内容
                                const xhr = new XMLHttpRequest();
                                xhr.open('GET', 'js/vue.global.js', false);
                                xhr.send();
                                const vueCode = xhr.responseText;
                                
                                // 修改代码以确保Vue被正确暴露
                                const modifiedCode = vueCode.replace(
                                    'var Vue = (function (exports) {',
                                    'window.Vue = (function (exports) {'
                                );
                                
                                eval(modifiedCode);
                                return window.Vue;
                            })();
                        }
                    `);
                } catch (e) {
                    log('Error trying to fix Vue: ' + e.message);
                }
            }
            
            if (typeof Vue !== 'undefined') {
                log('Vue is now available!');
                log('Vue version: ' + Vue.version);
                
                try {
                    const { createApp } = Vue;
                    const app = createApp({
                        data() {
                            return {
                                message: 'Vue is working!'
                            }
                        }
                    });
                    app.mount('#app');
                    log('Vue app mounted successfully!');
                } catch (e) {
                    log('Error mounting Vue app: ' + e.message);
                }
            } else {
                log('Vue is still not available');
                document.getElementById('app').innerHTML = 'Vue failed to load';
            }
        };
        
        script.onerror = function() {
            log('Failed to load Vue script');
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
