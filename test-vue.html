<!DOCTYPE html>
<html>
<head>
    <title>Vue Test</title>
</head>
<body>
    <div id="app">{{ message }}</div>
    
    <script src="js/vue.global.js"></script>
    <script>
        console.log('Script loaded');
        console.log('Vue:', typeof Vue);
        
        if (typeof Vue !== 'undefined') {
            console.log('Vue version:', Vue.version);
            const { createApp } = Vue;
            
            createApp({
                data() {
                    return {
                        message: 'Hello Vue!'
                    }
                }
            }).mount('#app');
        } else {
            document.getElementById('app').innerHTML = 'Vue not loaded';
        }
    </script>
</body>
</html>
