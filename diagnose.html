<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用诊断工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        h2 {
            color: #ff6b9d;
            margin-top: 0;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 5px solid #17a2b8;
        }
        button {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        button:hover {
            background: #ff5a8f;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .link-button {
            display: inline-block;
            background: #17a2b8;
            text-decoration: none;
            text-align: center;
        }
        .link-button:hover {
            background: #138496;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        pre {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
        .solution {
            background: #e2f0ff;
            border-left: 5px solid #007bff;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
        .file-list {
            font-family: monospace;
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>应用诊断工具</h1>
        
        <div class="section">
            <h2>重要提示</h2>
            <div class="result warning">
                <p><strong>请确保您是通过HTTP服务器访问此页面！</strong></p>
                <p>直接打开HTML文件（使用file://协议）会导致功能异常。</p>
                <p>正确方式：</p>
                <ol>
                    <li>双击 <code>start-server.bat</code> 启动服务器</li>
                    <li>在浏览器中访问 <code>http://localhost:8009/diagnose.html</code></li>
                </ol>
            </div>
        </div>
        
        <div class="section">
            <h2>文件结构检查</h2>
            <button id="checkFilesBtn" onclick="checkFiles()">检查文件结构</button>
            <div id="filesResult"></div>
        </div>
        
        <div class="section">
            <h2>Vue.js 加载测试</h2>
            <button id="testVueBtn" onclick="testVueLoading()">测试Vue.js加载</button>
            <div id="vueResult"></div>
        </div>
        
        <div class="section">
            <h2>功能访问</h2>
            <p>根据检查结果选择合适的版本：</p>
            <a href="index-no-vue.html" class="link-button">原生JS版本（推荐）</a>
            <a href="index.html" class="link-button">Vue.js版本（如果Vue正常）</a>
            <a href="./" class="link-button">返回主页</a>
        </div>
    </div>

    <script>
        // 检查文件结构
        function checkFiles() {
            const btn = document.getElementById('checkFilesBtn');
            btn.innerHTML = '<span class="loading"></span>检查中...';
            btn.disabled = true;
            
            const files = [
                { path: 'js/vue.global.js', name: 'Vue.js库文件' },
                { path: 'js/vue-app.js', name: 'Vue应用文件' },
                { path: 'css/style.css', name: '样式文件' },
                { path: 'assets/audio/romantic-musi.mp3', name: '音频文件' },
                { path: 'assets/icon-192x192.png', name: '图标文件' },
                { path: 'assets/icon-512x512.png', name: '大图标文件' }
            ];
            
            const resultsDiv = document.getElementById('filesResult');
            resultsDiv.innerHTML = '';
            
            let allExist = true;
            
            files.forEach(file => {
                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', file.path, false);
                
                try {
                    xhr.send();
                    if (xhr.status >= 200 && xhr.status < 400) {
                        const result = document.createElement('div');
                        result.className = 'result success';
                        result.textContent = `✅ ${file.name} 存在`;
                        resultsDiv.appendChild(result);
                    } else {
                        allExist = false;
                        const result = document.createElement('div');
                        result.className = 'result error';
                        result.textContent = `❌ ${file.name} 不存在 (${file.path})`;
                        resultsDiv.appendChild(result);
                    }
                } catch (e) {
                    allExist = false;
                    const result = document.createElement('div');
                    result.className = 'result error';
                    if (e.message.includes('Failed to execute \'send\' on \'XMLHttpRequest\'')) {
                        result.innerHTML = `❌ 文件检查失败: 您似乎直接打开了HTML文件，请通过HTTP服务器访问<br>
                        <div class="solution">
                            <h4>解决方案:</h4>
                            <p>1. 关闭当前页面</p>
                            <p>2. 双击项目目录中的 <code>start-server.bat</code> 文件</p>
                            <p>3. 在浏览器中访问 <code>http://localhost:8009/diagnose.html</code></p>
                        </div>`;
                    } else {
                        result.textContent = `❌ 检查 ${file.name} 时出错: ${e.message}`;
                    }
                    resultsDiv.appendChild(result);
                    return;
                }
            });
            
            if (allExist) {
                const result = document.createElement('div');
                result.className = 'result success';
                result.innerHTML = '<strong>✅ 所有文件都存在</strong><br>文件结构完整，可以继续检查Vue.js加载。';
                resultsDiv.appendChild(result);
                document.getElementById('testVueBtn').disabled = false;
            }
            
            btn.textContent = '检查文件结构';
            btn.disabled = false;
        }
        
        // 测试Vue.js加载
        function testVueLoading() {
            const btn = document.getElementById('testVueBtn');
            btn.innerHTML = '<span class="loading"></span>测试中...';
            btn.disabled = true;
            
            const resultsDiv = document.getElementById('vueResult');
            resultsDiv.innerHTML = '';
            
            // 创建script元素动态加载Vue.js
            const script = document.createElement('script');
            script.src = 'js/vue.global.js';
            
            script.onload = function() {
                try {
                    // 检查Vue对象
                    if (typeof Vue === 'undefined') {
                        throw new Error('Vue对象未定义');
                    }
                    
                    // 检查Vue版本
                    const version = Vue.version || '未知';
                    if (!version.startsWith('3.')) {
                        throw new Error(`Vue版本不兼容: ${version}`);
                    }
                    
                    // 检查必要API
                    const requiredAPIs = ['createApp', 'ref', 'reactive'];
                    const missingAPIs = [];
                    requiredAPIs.forEach(api => {
                        if (typeof Vue[api] === 'undefined') {
                            missingAPIs.push(api);
                        }
                    });
                    
                    if (missingAPIs.length > 0) {
                        throw new Error(`缺少必要API: ${missingAPIs.join(', ')}`);
                    }
                    
                    // 显示成功结果
                    const result = document.createElement('div');
                    result.className = 'result success';
                    result.innerHTML = `<strong>✅ Vue.js加载成功</strong><br>
                    Vue版本: ${version}<br>
                    所有必要的API都可用<br><br>
                    <a href="index.html" class="link-button">尝试Vue.js版本</a>`;
                    resultsDiv.appendChild(result);
                } catch (error) {
                    const result = document.createElement('div');
                    result.className = 'result error';
                    result.innerHTML = `<strong>❌ Vue.js功能检查失败</strong><br>
                    错误信息: ${error.message}<br><br>
                    <div class="solution">
                        <h4>解决方案:</h4>
                        <p>1. Vue.js文件可能已损坏，尝试重新下载</p>
                        <p>2. 使用原生JS版本以确保功能正常</p>
                    </div>
                    <a href="index-no-vue.html" class="link-button">使用原生JS版本</a>`;
                    resultsDiv.appendChild(result);
                }
                
                btn.textContent = '测试Vue.js加载';
                btn.disabled = false;
            };
            
            script.onerror = function() {
                const result = document.createElement('div');
                result.className = 'result error';
                result.innerHTML = `<strong>❌ Vue.js加载失败</strong><br>
                无法加载 js/vue.global.js 文件<br><br>
                <div class="solution">
                    <h4>解决方案:</h4>
                    <p>1. 检查 js/vue.global.js 文件是否存在且完整</p>
                    <p>2. 清除浏览器缓存后重试</p>
                    <p>3. 使用原生JS版本以确保功能正常</p>
                </div>
                <a href="index-no-vue.html" class="link-button">使用原生JS版本</a>`;
                resultsDiv.appendChild(result);
                
                btn.textContent = '测试Vue.js加载';
                btn.disabled = false;
            };
            
            document.head.appendChild(script);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认禁用Vue测试按钮
            document.getElementById('testVueBtn').disabled = true;
        });
    </script>
</body>
</html>