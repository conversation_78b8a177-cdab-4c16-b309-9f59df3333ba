<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-suite {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .passed {
            border-left: 5px solid green;
        }
        .failed {
            border-left: 5px solid red;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>心动告白应用测试套件</h1>
    
    <div class="test-suite">
        <h2>Vue.js 功能测试</h2>
        <button onclick="runVueTests()">运行Vue.js测试</button>
        <div id="vue-test-results"></div>
    </div>
    
    <div class="test-suite">
        <h2>原生JS功能测试</h2>
        <button onclick="runNativeTests()">运行原生JS测试</button>
        <div id="native-test-results"></div>
    </div>
    
    <div class="test-suite">
        <h2>PWA功能测试</h2>
        <button onclick="runPWATests()">运行PWA测试</button>
        <div id="pwa-test-results"></div>
    </div>
    
    <script>
        // 测试结果存储
        let testResults = [];
        
        // 添加测试结果
        function addResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const testCase = document.createElement('div');
            testCase.className = `test-case ${passed ? 'passed' : 'failed'}`;
            testCase.innerHTML = `<strong>${testName}</strong>: ${passed ? '通过' : '失败'} ${message ? '- ' + message : ''}`;
            container.appendChild(testCase);
            testResults.push({testName, passed, message});
        }
        
        // 清空测试结果
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
            testResults = [];
        }
        
        // Vue.js测试
        function runVueTests() {
            clearResults('vue-test-results');
            
            // 检查Vue是否加载
            const vueLoaded = typeof Vue !== 'undefined';
            addResult('vue-test-results', 'Vue.js加载', vueLoaded, vueLoaded ? '' : 'Vue对象未定义');
            
            // 检查Vue版本
            if (vueLoaded) {
                const version = Vue.version || '未知';
                addResult('vue-test-results', 'Vue.js版本', true, version);
            }
            
            // 检查createApp函数
            const createAppExists = vueLoaded && typeof Vue.createApp !== 'undefined';
            addResult('vue-test-results', 'createApp函数', createAppExists, createAppExists ? '' : 'createApp函数不存在');
            
            // 检查响应式API
            const reactivityAPI = vueLoaded && 
                typeof Vue.ref !== 'undefined' && 
                typeof Vue.reactive !== 'undefined' && 
                typeof Vue.computed !== 'undefined';
            addResult('vue-test-results', '响应式API', reactivityAPI, reactivityAPI ? '' : '部分响应式API缺失');
            
            // 检查生命周期钩子
            const lifecycleHooks = vueLoaded && 
                typeof Vue.onMounted !== 'undefined' && 
                typeof Vue.onUnmounted !== 'undefined';
            addResult('vue-test-results', '生命周期钩子', lifecycleHooks, lifecycleHooks ? '' : '生命周期钩子缺失');
        }
        
        // 原生JS测试
        function runNativeTests() {
            clearResults('native-test-results');
            
            // 检查基本DOM API
            const domAPI = typeof document !== 'undefined' && typeof window !== 'undefined';
            addResult('native-test-results', 'DOM API', domAPI, domAPI ? '' : '基本DOM API不可用');
            
            // 检查localStorage支持
            let localStorageSupported = false;
            try {
                localStorageSupported = typeof localStorage !== 'undefined';
                if (localStorageSupported) {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                }
            } catch (e) {
                localStorageSupported = false;
            }
            addResult('native-test-results', 'localStorage支持', localStorageSupported, localStorageSupported ? '' : 'localStorage不可用');
            
            // 检查Audio支持
            const audioSupported = typeof Audio !== 'undefined';
            addResult('native-test-results', 'Audio支持', audioSupported, audioSupported ? '' : 'Audio API不可用');
            
            // 检查Canvas支持
            const canvasSupported = (() => {
                const canvas = document.createElement('canvas');
                return !!(canvas.getContext && canvas.getContext('2d'));
            })();
            addResult('native-test-results', 'Canvas支持', canvasSupported, canvasSupported ? '' : 'Canvas API不可用');
            
            // 检查触摸事件支持
            const touchSupported = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            addResult('native-test-results', '触摸支持', true, touchSupported ? '支持触摸' : '不支持触摸（或在桌面设备上）');
        }
        
        // PWA测试
        function runPWATests() {
            clearResults('pwa-test-results');
            
            // 检查Service Worker支持
            const swSupported = 'serviceWorker' in navigator;
            addResult('pwa-test-results', 'Service Worker支持', swSupported, swSupported ? '' : 'Service Worker API不可用');
            
            // 检查Web App Manifest
            const manifest = document.querySelector('link[rel="manifest"]');
            const manifestExists = !!manifest;
            addResult('pwa-test-results', 'Web App Manifest', manifestExists, manifestExists ? '' : '未找到manifest文件');
            
            // 检查PWA安装支持
            const installSupported = 'beforeinstallprompt' in window;
            addResult('pwa-test-results', 'PWA安装支持', installSupported, installSupported ? '' : 'beforeinstallprompt事件不可用');
            
            // 检查是否已安装为PWA
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            addResult('pwa-test-results', 'PWA安装状态', true, isStandalone ? '已安装为PWA' : '未安装为PWA');
            
            // 检查缓存API
            const cacheSupported = 'caches' in window;
            addResult('pwa-test-results', '缓存API支持', cacheSupported, cacheSupported ? '' : 'Cache API不可用');
        }
    </script>
</body>
</html>