<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-app {
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            background: #f8fff8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vue.js 简单测试</h1>
        
        <div id="status">
            <p>正在检查Vue.js...</p>
        </div>
        
        <div id="test-mount-point"></div>
        
        <div>
            <button onclick="testBasicVue()">测试基本Vue功能</button>
            <button onclick="testReactivity()">测试响应式</button>
            <button onclick="window.location.href='index.html'">前往主应用</button>
            <button onclick="window.location.href='index-no-vue.html'">使用原生JS版本</button>
        </div>
    </div>

    <!-- 加载Vue.js -->
    <script src="js/vue.global.js"></script>
    
    <script>
        // 检查Vue是否加载成功
        function checkVueLoaded() {
            const statusDiv = document.getElementById('status');
            
            if (typeof Vue === 'undefined') {
                statusDiv.innerHTML = '<div class="error">❌ Vue.js 未加载成功</div>';
                return false;
            }
            
            if (!Vue.version || !Vue.version.startsWith('3.')) {
                statusDiv.innerHTML = `<div class="error">❌ Vue版本不兼容: ${Vue.version || '未知'}</div>`;
                return false;
            }
            
            const requiredAPIs = ['createApp', 'ref', 'reactive'];
            const missingAPIs = requiredAPIs.filter(api => typeof Vue[api] === 'undefined');
            
            if (missingAPIs.length > 0) {
                statusDiv.innerHTML = `<div class="error">❌ 缺少必要的API: ${missingAPIs.join(', ')}</div>`;
                return false;
            }
            
            statusDiv.innerHTML = `<div class="success">✅ Vue.js ${Vue.version} 加载成功！</div>`;
            return true;
        }
        
        // 测试基本Vue功能
        function testBasicVue() {
            if (!checkVueLoaded()) return;
            
            try {
                const { createApp, ref } = Vue;
                
                const app = createApp({
                    setup() {
                        const message = ref('Hello Vue 3!');
                        const count = ref(0);
                        
                        const increment = () => {
                            count.value++;
                        };
                        
                        return {
                            message,
                            count,
                            increment
                        };
                    },
                    template: `
                        <div class="test-app">
                            <h3>{{ message }}</h3>
                            <p>计数器: {{ count }}</p>
                            <button @click="increment">点击增加</button>
                        </div>
                    `
                });
                
                // 清空之前的测试
                const mountPoint = document.getElementById('test-mount-point');
                mountPoint.innerHTML = '<div id="vue-test-app"></div>';
                
                app.mount('#vue-test-app');
                
                document.getElementById('status').innerHTML += 
                    '<div class="success">✅ Vue应用创建成功！</div>';
                    
            } catch (error) {
                document.getElementById('status').innerHTML += 
                    `<div class="error">❌ Vue应用创建失败: ${error.message}</div>`;
            }
        }
        
        // 测试响应式功能
        function testReactivity() {
            if (!checkVueLoaded()) return;
            
            try {
                const { createApp, ref, reactive, computed } = Vue;
                
                const app = createApp({
                    setup() {
                        const state = reactive({
                            name: '测试用户',
                            age: 25
                        });
                        
                        const doubleAge = computed(() => state.age * 2);
                        
                        const updateAge = () => {
                            state.age = Math.floor(Math.random() * 50) + 18;
                        };
                        
                        return {
                            state,
                            doubleAge,
                            updateAge
                        };
                    },
                    template: `
                        <div class="test-app">
                            <h3>响应式测试</h3>
                            <p>姓名: {{ state.name }}</p>
                            <p>年龄: {{ state.age }}</p>
                            <p>双倍年龄: {{ doubleAge }}</p>
                            <button @click="updateAge">随机更新年龄</button>
                        </div>
                    `
                });
                
                // 清空之前的测试
                const mountPoint = document.getElementById('test-mount-point');
                mountPoint.innerHTML = '<div id="vue-reactivity-test"></div>';
                
                app.mount('#vue-reactivity-test');
                
                document.getElementById('status').innerHTML += 
                    '<div class="success">✅ Vue响应式功能正常！</div>';
                    
            } catch (error) {
                document.getElementById('status').innerHTML += 
                    `<div class="error">❌ Vue响应式测试失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(checkVueLoaded, 100);
        });
    </script>
</body>
</html>
