const CACHE_NAME = 'heart-confession-v2';
const ASSETS = [
  '/',
  '/index.html',
  '/index-no-vue.html',
  '/js/vue-app.js',
  '/js/vue.global.js',
  '/css/style.css',
  '/assets/audio/romantic-musi.mp3',
  '/assets/icon-192x192.png',
  '/assets/icon-512x512.png',
  '/manifest.json'
];

// 安装事件 - 缓存资源
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching assets');
        return cache.addAll(ASSETS);
      })
      .catch(error => {
        console.error('Failed to cache assets:', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // 立即接管页面控制
  return self.clients.claim();
});

// 获取事件 - 拦截网络请求并提供缓存资源
self.addEventListener('fetch', (event) => {
  // 对于非GET请求，直接发送网络请求
  if (event.request.method !== 'GET') {
    return;
  }
  
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // 如果缓存中有响应，返回缓存的资源
        if (response) {
          console.log('Cache hit:', event.request.url);
          return response;
        }
        
        // 否则发送网络请求
        console.log('Cache miss, fetching from network:', event.request.url);
        return fetch(event.request)
          .then(networkResponse => {
            // 对于重要资源，更新缓存
            if (event.request.destination === 'document' || 
                event.request.destination === 'script' || 
                event.request.destination === 'style') {
              const responseToCache = networkResponse.clone();
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });
            }
            return networkResponse;
          })
          .catch(error => {
            // 网络请求失败时的处理
            console.error('Network request failed:', error);
            
            // 对于文档请求，返回离线页面
            if (event.request.destination === 'document') {
              return caches.match('/index-no-vue.html');
            }
            
            // 返回错误响应
            return new Response('Offline', {
              status: 503,
              statusText: 'Service Unavailable'
            });
          });
      })
      .catch(error => {
        console.error('Caching error:', error);
        return fetch(event.request);
      })
  );
});

// 消息处理
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});