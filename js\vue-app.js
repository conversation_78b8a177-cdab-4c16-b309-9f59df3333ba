// Vue.js 3 Heart Confession App
const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick, onErrorCaptured } = Vue || {};

// 检查Vue是否正确加载
if (typeof Vue === 'undefined') {
    console.error('Vue is not loaded. Please check the vue.global.js file.');
    throw new Error('Vue is not loaded. Please check the vue.global.js file.');
}

if (!Vue.createApp) {
    console.error('Vue.createApp is not available. Vue version might be incompatible.');
    throw new Error('Vue.createApp is not available. Vue version might be incompatible.');
}

// 简化的心形渲染器
class SimpleHeartRenderer {
    constructor(canvas) {
        if (!canvas) {
            throw new Error('Canvas element is required');
        }
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        if (!this.ctx) {
            throw new Error('Canvas 2D context is not supported');
        }
        this.animationFrame = null;
        this.scale = 1;
        this.init();
    }
    
    init() {
        try {
            this.setupCanvas();
            this.draw();
        } catch (error) {
            console.error('Failed to initialize heart renderer:', error);
            throw error;
        }
    }
    
    setupCanvas() {
        try {
            const size = Math.min(window.innerWidth - 40, 300);
            this.canvas.width = size;
            this.canvas.height = size;
            this.canvas.style.width = size + 'px';
            this.canvas.style.height = size + 'px';
        } catch (error) {
            console.error('Failed to setup canvas:', error);
            throw error;
        }
    }
    
    draw() {
        try {
            const ctx = this.ctx;
            const width = this.canvas.width;
            const height = this.canvas.height;
            
            // 清除画布
            ctx.clearRect(0, 0, width, height);
            
            // 绘制心形
            ctx.save();
            ctx.translate(width / 2, height / 2);
            ctx.scale(this.scale, this.scale);
            
            // 心形路径
            ctx.beginPath();
            ctx.fillStyle = '#ff6b9d';
            
            const size = Math.min(width, height) * 0.3;
            for (let t = 0; t <= Math.PI * 2; t += 0.01) {
                const x = size * (16 * Math.pow(Math.sin(t), 3));
                const y = -size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));
                
                if (t === 0) {
                    ctx.moveTo(x / 16, y / 16);
                } else {
                    ctx.lineTo(x / 16, y / 16);
                }
            }
            
            ctx.closePath();
            ctx.fill();
            
            // 添加阴影效果
            ctx.shadowColor = 'rgba(255, 107, 157, 0.5)';
            ctx.shadowBlur = 20;
            ctx.fill();
            
            ctx.restore();
        } catch (error) {
            console.error('Failed to draw heart:', error);
        }
    }
    
    animateClick() {
        try {
            // 简单的缩放动画
            this.scale = 1.2;
            this.draw();
            
            setTimeout(() => {
                this.scale = 1;
                this.draw();
            }, 300);
        } catch (error) {
            console.error('Failed to animate click:', error);
        }
    }
    
    destroy() {
        try {
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
            }
        } catch (error) {
            console.error('Failed to destroy heart renderer:', error);
        }
    }
}

// 简化的配置对象
const APP_CONFIG = {
    APP_NAME: '心动告白',
    DEFAULT_MESSAGES: [
        "你知道吗？你的笑容是我见过最美的风景。",
        "每当我看到你，我的心跳都会不由自主地加速。",
        "和你在一起的每一刻，都让我感到无比幸福。",
        "你的温柔如春风，轻抚着我的心田。",
        "我想牵着你的手，走过人生的每一个春夏秋冬。",
        "我爱你，不只是今天，而是每一个明天。"
    ],
    ANIMATION: {
        HEART_EXPLOSION_DURATION: 600,
        TOAST_DURATION: 2000
    },
    SUCCESS_MESSAGES: {
        CUSTOM_MESSAGES_SAVED: '自定义消息已保存',
        DEFAULT_MESSAGES_RESTORED: '已恢复默认消息',
        LINK_COPIED: '链接已复制到剪贴板',
        APP_INSTALLED: '应用已添加到桌面'
    },
    ERROR_MESSAGES: {
        SHARE_FAILED: '分享失败，请手动复制链接',
        SCREENSHOT_FAILED: '请使用浏览器的截图功能',
        SAVE_FAILED: '保存失败，请重试',
        VUE_ERROR: '应用出现错误，请刷新页面重试',
        INITIALIZATION_FAILED: '应用初始化失败'
    }
};

// 添加全局错误处理
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

const HeartConfessionApp = {
    setup() {
        // 响应式数据
        const currentMessage = ref(0);
        const isAnimating = ref(false);
        const isMusicPlaying = ref(false);
        const canInstall = ref(false);
        const showSettingsModal = ref(false);
        const messageKey = ref(0);
        const appError = ref(null);
        const isLoading = ref(true);
        
        // 消息数据
        const messages = ref([...APP_CONFIG.DEFAULT_MESSAGES]);
        const customMessages = ref([...APP_CONFIG.DEFAULT_MESSAGES]);
        
        // Toast通知
        const toast = reactive({
            show: false,
            message: '',
            type: 'success'
        });
        
        // 模板引用
        const heartCanvas = ref(null);
        const bgMusic = ref(null);
        
        // 简化的心形渲染器
        let heartRenderer = null;
        
        // 计算属性
        const currentMessageText = computed(() => {
            return messages.value[currentMessage.value] || '准备好接受我的告白了吗？';
        });
        
        const heartPromptText = computed(() => {
            if (currentMessage.value === 0) {
                return '点击❤️开始告白';
            }
            const remaining = 6 - currentMessage.value;
            return remaining > 0 ? `继续点击❤️ (${remaining}次)` : '点击❤️重新开始';
        });
        
        // 方法
        const showToast = (message, type = 'success') => {
            toast.message = message;
            toast.type = type;
            toast.show = true;
            
            setTimeout(() => {
                toast.show = false;
            }, APP_CONFIG.ANIMATION.TOAST_DURATION);
        };
        
        const initializeHeartRenderer = async () => {
            try {
                await nextTick();
                if (heartCanvas.value) {
                    heartRenderer = new SimpleHeartRenderer(heartCanvas.value);
                }
                console.log('Heart renderer initialized');
            } catch (error) {
                console.error('Failed to initialize heart renderer:', error);
                appError.value = '心形渲染器初始化失败';
                showToast('心形渲染器初始化失败', 'error');
            }
        };

        const initializeAudio = () => {
            try {
                const audio = bgMusic.value;
                console.log('Initializing audio, element:', audio);

                if (!audio) {
                    console.error('Audio element not found');
                    showToast('音频元素未找到', 'error');
                    return;
                }

                // 设置初始音量
                audio.volume = 0.3;

                // 音频加载完成
                audio.addEventListener('canplaythrough', () => {
                    console.log('Audio loaded successfully');
                    showToast('音频已加载', 'success');
                });

                // 音频加载错误
                audio.addEventListener('error', (e) => {
                    console.error('Audio loading error:', e);
                    showToast('音频文件加载失败', 'error');
                });

                // 音频播放开始
                audio.addEventListener('play', () => {
                    console.log('Audio play event');
                    isMusicPlaying.value = true;
                });

                // 音频暂停
                audio.addEventListener('pause', () => {
                    console.log('Audio pause event');
                    isMusicPlaying.value = false;
                });

                // 音频播放结束（循环播放时不会触发）
                audio.addEventListener('ended', () => {
                    console.log('Audio ended event');
                    isMusicPlaying.value = false;
                    // 自动循环播放
                    audio.play().catch(err => {
                        console.log('Auto replay failed:', err);
                    });
                });

                console.log('Audio initialization complete');
            } catch (error) {
                console.error('Audio initialization error:', error);
                appError.value = '音频初始化失败';
                showToast('音频初始化失败: ' + error.message, 'error');
            }
        };
        
        const loadUserPreferences = () => {
            try {
                // 从localStorage加载自定义消息
                const saved = localStorage.getItem('heartapp_custom_messages');
                if (saved) {
                    const savedMessages = JSON.parse(saved);
                    if (savedMessages && savedMessages.length === 6) {
                        messages.value = [...savedMessages];
                        customMessages.value = [...savedMessages];
                    }
                }
                console.log('User preferences loaded');
            } catch (error) {
                console.error('Failed to load user preferences:', error);
                showToast('加载用户设置失败', 'error');
            }
        };
        
        const saveUserPreferences = () => {
            try {
                localStorage.setItem('heartapp_custom_messages', JSON.stringify(messages.value));
            } catch (error) {
                console.error('Failed to save user preferences:', error);
                showToast('保存用户设置失败', 'error');
            }
        };
        
        const handleHeartClick = async (event) => {
            if (isAnimating.value) return;

            // 第一次点击时尝试自动播放音乐
            if (currentMessage.value === 0 && !isMusicPlaying.value) {
                tryAutoPlayMusic();
            }

            isAnimating.value = true;

            // 创建简单的点击效果
            createClickEffect(event);

            // 心形动画
            if (heartRenderer) {
                heartRenderer.animateClick();
            }

            // 显示下一条消息
            showNextMessage();

            // 创建心形粒子效果
            createHeartParticles();

            setTimeout(() => {
                isAnimating.value = false;
            }, APP_CONFIG.ANIMATION.HEART_EXPLOSION_DURATION);
        };

        const tryAutoPlayMusic = async () => {
            try {
                const audio = bgMusic.value;
                if (audio && !isMusicPlaying.value) {
                    audio.volume = 0.3;
                    await audio.play();
                    isMusicPlaying.value = true;
                    console.log('Auto-play music started');
                }
            } catch (error) {
                console.log('Auto-play failed (this is normal):', error.message);
                // 自动播放失败是正常的，提示用户手动播放
                showToast('点击音乐按钮开始播放背景音乐', 'info');
            }
        };
        
        const createClickEffect = (event) => {
            if (!heartCanvas.value) return;
            
            const rect = heartCanvas.value.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 创建波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                left: ${rect.left + x - 25}px;
                top: ${rect.top + y - 25}px;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: rgba(255, 107, 157, 0.6);
                transform: scale(0);
                pointer-events: none;
                z-index: 1000;
            `;
            
            document.body.appendChild(ripple);
            
            // 动画效果
            ripple.animate([
                { transform: 'scale(0)', opacity: 1 },
                { transform: 'scale(2)', opacity: 0 }
            ], {
                duration: 600,
                easing: 'ease-out'
            }).onfinish = () => {
                if (document.body.contains(ripple)) {
                    document.body.removeChild(ripple);
                }
            };
        };
        
        const createHeartParticles = () => {
            if (!heartCanvas.value) return;
            
            const rect = heartCanvas.value.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            // 创建多个心形粒子
            for (let i = 0; i < 12; i++) { // 增加粒子数量
                const particle = document.createElement('div');
                particle.textContent = '❤️';
                particle.style.cssText = `
                    position: absolute;
                    left: ${centerX}px;
                    top: ${centerY}px;
                    font-size: ${Math.random() * 20 + 10}px; // 随机大小
                    pointer-events: none;
                    z-index: 1000;
                    opacity: ${Math.random() * 0.5 + 0.5}; // 随机透明度
                    animation: floatParticle 1.5s ease-out forwards;
                    filter: hue-rotate(${Math.random() * 30 - 15}deg); // 轻微颜色变化
                `;
                
                // 添加粒子动画样式
                if (!document.getElementById('particle-animation')) {
                    const style = document.createElement('style');
                    style.id = 'particle-animation';
                    style.textContent = `
                        @keyframes floatParticle {
                            0% { 
                                transform: translate(-50%, -50%) scale(1); 
                                opacity: 1; 
                            }
                            100% { 
                                transform: translate(
                                    ${(Math.random() - 0.5) * 200}px, 
                                    ${-100 - Math.random() * 100}px
                                ) scale(0.2); 
                                opacity: 0; 
                            }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                document.body.appendChild(particle);
                
                // 粒子动画结束后移除
                setTimeout(() => {
                    if (document.body.contains(particle)) {
                        document.body.removeChild(particle);
                    }
                }, 1500);
            }
            
            // 添加光晕效果
            const glow = document.createElement('div');
            glow.style.cssText = `
                position: absolute;
                left: ${centerX}px;
                top: ${centerY}px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255,107,157,0.8), transparent 70%);
                pointer-events: none;
                z-index: 999;
                transform: translate(-50%, -50%);
                animation: glowPulse 0.8s ease-out forwards;
            `;
            
            // 添加光晕动画样式
            if (!document.getElementById('glow-animation')) {
                const style = document.createElement('style');
                style.id = 'glow-animation';
                style.textContent = `
                    @keyframes glowPulse {
                        0% { 
                            transform: translate(-50%, -50%) scale(1); 
                            opacity: 1; 
                        }
                        100% { 
                            transform: translate(-50%, -50%) scale(8); 
                            opacity: 0; 
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(glow);
            
            // 光晕动画结束后移除
            setTimeout(() => {
                if (document.body.contains(glow)) {
                    document.body.removeChild(glow);
                }
            }, 800);
        };
        
        const showNextMessage = () => {
            // 更新当前消息索引
            currentMessage.value = (currentMessage.value + 1) % messages.value.length;
            
            // 强制重新渲染消息文本
            messageKey.value++;
        };
        
        const toggleMusic = async () => {
            console.log('toggleMusic called, current state:', isMusicPlaying.value);

            try {
                const audio = bgMusic.value;
                console.log('Audio element:', audio);
                console.log('Audio src:', audio?.src);
                console.log('Audio paused:', audio?.paused);

                if (!audio) {
                    console.error('Audio element not available');
                    showToast('音频不可用', 'error');
                    return;
                }

                if (isMusicPlaying.value || !audio.paused) {
                    console.log('Pausing audio');
                    audio.pause();
                    isMusicPlaying.value = false;
                    showToast('音乐已关闭', 'success');
                } else {
                    console.log('Playing audio');
                    // 设置音量
                    audio.volume = 0.3; // 设置为30%音量，避免过大

                    try {
                        await audio.play();
                        isMusicPlaying.value = true;
                        showToast('音乐已开启', 'success');
                        console.log('Audio play successful');
                    } catch (playError) {
                        console.error('Audio play failed:', playError);
                        if (playError.name === 'NotAllowedError') {
                            showToast('请先与页面交互后再播放音乐', 'warning');
                        } else if (playError.name === 'NotSupportedError') {
                            showToast('音频格式不支持', 'error');
                        } else {
                            showToast(`音乐播放失败: ${playError.message}`, 'error');
                        }
                    }
                }
            } catch (error) {
                console.error('Music toggle failed:', error);
                showToast(`音乐功能异常: ${error.message}`, 'error');
            }
        };
        
        const shareApp = async () => {
            try {
                if (navigator.share) {
                    await navigator.share({
                        title: APP_CONFIG.APP_NAME,
                        text: '用这个浪漫的告白应用表达你的心意吧！',
                        url: window.location.href
                    });
                } else {
                    // Fallback: copy to clipboard
                    await navigator.clipboard.writeText(window.location.href);
                    showToast(APP_CONFIG.SUCCESS_MESSAGES.LINK_COPIED, 'success');
                }
            } catch (error) {
                console.log('Share failed:', error);
                showToast(APP_CONFIG.ERROR_MESSAGES.SHARE_FAILED, 'error');
            }
        };
        
        const installApp = async () => {
            try {
                // 简化的PWA安装
                if (window.deferredPrompt) {
                    window.deferredPrompt.prompt();
                    const { outcome } = await window.deferredPrompt.userChoice;
                    if (outcome === 'accepted') {
                        showToast(APP_CONFIG.SUCCESS_MESSAGES.APP_INSTALLED, 'success');
                    }
                    window.deferredPrompt = null;
                    canInstall.value = false;
                } else {
                    showToast('请使用浏览器的"添加到主屏幕"功能', 'warning');
                }
            } catch (error) {
                console.error('Install failed:', error);
                showToast('安装失败，请重试', 'error');
            }
        };
        
        const takeScreenshot = () => {
            // 显示闪光效果
            const flash = document.createElement('div');
            flash.className = 'screenshot-flash';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 300);
            
            // 使用html2canvas截图（如果可用）
            if (typeof html2canvas !== 'undefined') {
                html2canvas(document.body).then(canvas => {
                    const link = document.createElement('a');
                    link.download = `heart-confession-${Date.now()}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                    showToast('截图已保存', 'success');
                }).catch(error => {
                    console.error('Screenshot failed:', error);
                    showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
                });
            } else {
                showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
            }
        };
        
        const openSettings = () => {
            // 加载当前消息到自定义消息数组
            customMessages.value = [...messages.value];
            showSettingsModal.value = true;
        };
        
        const closeSettings = () => {
            showSettingsModal.value = false;
        };
        
        const closeModalOnBackdrop = (event) => {
            if (event.target.classList.contains('modal')) {
                closeSettings();
            }
        };
        
        const getPlaceholder = (index) => {
            const placeholders = [
                '友好的赞美...',
                '温暖的关心...',
                '真诚的欣赏...',
                '深情的表达...',
                '浪漫的承诺...',
                '深情的告白...'
            ];
            return placeholders[index] || '输入告白语句...';
        };
        
        const saveCustomMessages = () => {
            try {
                const hasCustom = customMessages.value.some((msg, index) => 
                    msg.trim() !== '' && msg !== APP_CONFIG.DEFAULT_MESSAGES[index]
                );
                
                if (hasCustom) {
                    // 填充空消息为默认消息
                    const finalMessages = customMessages.value.map((msg, index) => 
                        msg.trim() || APP_CONFIG.DEFAULT_MESSAGES[index]
                    );
                    
                    messages.value = [...finalMessages];
                    saveUserPreferences();
                    showToast(APP_CONFIG.SUCCESS_MESSAGES.CUSTOM_MESSAGES_SAVED, 'success');
                    closeSettings();
                } else {
                    showToast('请至少修改一条消息', 'warning');
                }
            } catch (error) {
                console.error('Save failed:', error);
                showToast(APP_CONFIG.ERROR_MESSAGES.SAVE_FAILED, 'error');
            }
        };

        const resetToDefault = () => {
            customMessages.value = [...APP_CONFIG.DEFAULT_MESSAGES];
            messages.value = [...APP_CONFIG.DEFAULT_MESSAGES];
            try {
                localStorage.removeItem('heartapp_custom_messages');
            } catch (error) {
                console.error('Failed to remove custom messages:', error);
            }
            showToast(APP_CONFIG.SUCCESS_MESSAGES.DEFAULT_MESSAGES_RESTORED, 'success');
        };

        const debugMusic = async () => {
            const audio = bgMusic.value;
            console.log('Debug Music - Audio element:', audio);
            console.log('Debug Music - Audio src:', audio?.src);
            console.log('Debug Music - Audio paused:', audio?.paused);
            console.log('Debug Music - Audio volume:', audio?.volume);

            if (!audio) {
                showToast('音频元素不存在', 'error');
                return;
            }

            try {
                if (audio.paused) {
                    audio.volume = 0.3;
                    await audio.play();
                    showToast('调试：音乐开始播放', 'success');
                } else {
                    audio.pause();
                    showToast('调试：音乐已暂停', 'success');
                }
            } catch (error) {
                console.error('Debug music error:', error);
                showToast(`调试错误：${error.message}`, 'error');
            }
        };
        
        // 错误处理
        const handleError = (error, instance, info) => {
            console.error('Vue Error:', error);
            console.error('Component:', instance);
            console.error('Info:', info);
            appError.value = error.message;
            showToast(APP_CONFIG.ERROR_MESSAGES.VUE_ERROR, 'error');
        };
        
        // 生命周期钩子
        onMounted(async () => {
            try {
                isLoading.value = true;
                await initializeHeartRenderer();
                loadUserPreferences();

                // 初始化音频
                await nextTick();
                initializeAudio();

                // 检查PWA安装状态
                const checkInstallStatus = () => {
                    // 检查是否已经安装为PWA
                    if (window.matchMedia('(display-mode: standalone)').matches) {
                        console.log('应用已安装为PWA');
                    }
                    
                    // 监听安装提示事件
                    window.addEventListener('beforeinstallprompt', (e) => {
                        e.preventDefault();
                        window.deferredPrompt = e;
                        canInstall.value = true;
                        showToast('可以将此应用安装到桌面', 'info');
                    });
                    
                    // 监听安装完成事件
                    window.addEventListener('appinstalled', () => {
                        showToast('应用已成功安装到桌面', 'success');
                        window.deferredPrompt = null;
                        canInstall.value = false;
                    });
                };

                checkInstallStatus();

                // 注册Service Worker
                if ('serviceWorker' in navigator) {
                    window.addEventListener('load', () => {
                        navigator.serviceWorker.register('/sw.js')
                            .then(registration => {
                                console.log('ServiceWorker registration successful with scope: ', registration.scope);
                                
                                // 检查更新
                                registration.addEventListener('updatefound', () => {
                                    const newWorker = registration.installing;
                                    newWorker.addEventListener('statechange', () => {
                                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                            showToast('发现新版本，刷新页面以更新', 'info');
                                        }
                                    });
                                });
                            })
                            .catch(err => {
                                console.log('ServiceWorker registration failed: ', err);
                                showToast('PWA功能注册失败', 'warning');
                            });
                        
                        // 检查当前Service Worker状态
                        if (navigator.serviceWorker.controller) {
                            console.log('应用正由Service Worker控制');
                        }
                    });
                } else {
                    showToast('当前浏览器不支持PWA功能', 'warning');
                }

                console.log('Heart Confession Vue App mounted successfully');
                isLoading.value = false;
            } catch (error) {
                console.error('App mount error:', error);
                appError.value = APP_CONFIG.ERROR_MESSAGES.INITIALIZATION_FAILED;
                showToast('应用初始化失败: ' + error.message, 'error');
                isLoading.value = false;
            }
        });

        onUnmounted(() => {
            // 清理资源
            if (heartRenderer && typeof heartRenderer.destroy === 'function') {
                heartRenderer.destroy();
            }
            console.log('Heart Confession Vue App unmounted');
        });
        
        // 返回模板需要的数据和方法
        return {
            // 响应式数据
            currentMessage,
            isAnimating,
            isMusicPlaying,
            canInstall,
            showSettingsModal,
            messageKey,
            messages,
            customMessages,
            toast,
            appError,
            isLoading,
            
            // 模板引用
            heartCanvas,
            bgMusic,
            
            // 计算属性
            currentMessageText,
            heartPromptText,
            
            // 方法
            handleHeartClick,
            toggleMusic,
            shareApp,
            installApp,
            takeScreenshot,
            openSettings,
            closeSettings,
            closeModalOnBackdrop,
            getPlaceholder,
            saveCustomMessages,
            resetToDefault,
            debugMusic,
            handleError
        };
    }
};

// 创建并挂载Vue应用
try {
    const app = createApp(HeartConfessionApp);

    // 全局错误处理
    app.config.errorHandler = (error, instance, info) => {
        console.error('Global Vue Error:', error);
        console.error('Component:', instance);
        console.error('Info:', info);
    };

    // 捕获子组件错误
    app.config.warnHandler = (msg, instance, trace) => {
        console.warn('Vue Warning:', msg);
        console.warn('Component:', instance);
        console.warn('Trace:', trace);
    };

    app.mount('#app');
    console.log('Heart Confession Vue.js 3 App initialized successfully');
} catch (error) {
    console.error('Failed to initialize Vue app:', error);
    // 如果Vue应用初始化失败，则显示错误信息
    window.addEventListener('load', function() {
        const appElement = document.getElementById('app');
        if (appElement) {
            appElement.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #ff6b9d;">
                    <h2>Vue应用初始化失败</h2>
                    <p>错误信息: ${error.message}</p>
                    <button onclick="window.location.href='index-no-vue.html'" 
                            style="background: #ff6b9d; color: white; border: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; cursor: pointer; margin: 10px;">
                        使用原生JS版本
                    </button>
                    <button onclick="window.location.reload()" 
                            style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; cursor: pointer; margin: 10px;">
                        重试
                    </button>
                </div>
            `;
        }
    });
}