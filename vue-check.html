<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 加载诊断工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            color: #333;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 30px;
        }
        .important-note {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        button {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        button:hover {
            background: #ff5a8f;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        pre {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
        .solutions {
            background: #e2f0ff;
            border-left: 5px solid #007bff;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vue.js 加载诊断工具</h1>
        
        <div class="important-note">
            <h3>重要提示</h3>
            <p>请确保您是通过HTTP服务器访问此页面，而不是直接打开HTML文件。</p>
            <p>直接打开HTML文件会导致浏览器安全策略阻止文件加载，从而出现错误。</p>
            <p>正确的方式是：</p>
            <ol>
                <li>运行 start-server.bat 文件启动服务器</li>
                <li>在浏览器中访问 http://localhost:8009/vue-check.html</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>诊断步骤</h2>
            <p>点击下面的按钮逐步诊断Vue.js加载问题：</p>
            
            <button id="step1" onclick="checkFileExists()">步骤1: 检查Vue.js文件是否存在</button>
            <button id="step2" onclick="loadVueManually()" disabled>步骤2: 手动加载Vue.js</button>
            <button id="step3" onclick="checkVueAPI()" disabled>步骤3: 检查Vue API</button>
            <button id="step4" onclick="initVueApp()" disabled>步骤4: 初始化Vue应用</button>
            
            <div id="results"></div>
        </div>
        
        <div class="solutions hidden" id="solutions">
            <h3>推荐解决方案</h3>
            <div id="solution-content"></div>
        </div>
        
        <div class="test-section">
            <h2>备选方案</h2>
            <p>如果问题无法解决，您可以使用以下备选方案：</p>
            <button onclick="window.location.href='index-no-vue.html'">使用原生JS版本（推荐）</button>
            <button onclick="window.location.reload()">刷新页面重试</button>
            <button onclick="window.location.href='./'">返回主页</button>
        </div>
    </div>

    <script>
        let vueLoaded = false;
        
        // 添加结果到页面
        function addResult(message, type) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            document.getElementById('results').appendChild(resultDiv);
            
            // 滚动到结果区域
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        
        // 显示解决方案
        function showSolution(title, content) {
            const solutionsDiv = document.getElementById('solutions');
            document.getElementById('solution-content').innerHTML = `<h4>${title}</h4><p>${content}</p>`;
            solutionsDiv.classList.remove('hidden');
            
            // 滚动到解决方案
            solutionsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 步骤1: 检查文件是否存在
        function checkFileExists() {
            document.getElementById('step1').innerHTML = '<span class="loading"></span>检查中...';
            document.getElementById('step1').disabled = true;
            
            const files = [
                { path: 'js/vue.global.js', name: 'Vue.js库文件' },
                { path: 'js/vue-app.js', name: 'Vue应用文件' }
            ];
            
            let allExist = true;
            let missingFiles = [];
            
            files.forEach(file => {
                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', file.path, false); // 同步请求
                try {
                    xhr.send();
                    if (xhr.status >= 400) {
                        allExist = false;
                        missingFiles.push(file.name);
                        addResult(`❌ ${file.name} 不存在 (${file.path})`, 'error');
                    } else {
                        addResult(`✅ ${file.name} 存在`, 'success');
                    }
                } catch (e) {
                    allExist = false;
                    missingFiles.push(file.name);
                    // 检查是否是因为跨域问题导致的错误
                    if (e.message.includes('Failed to execute \'send\' on \'XMLHttpRequest\'')) {
                        addResult(`❌ 检查 ${file.name} 时出错: 您似乎直接打开了HTML文件，请通过HTTP服务器访问此页面`, 'error');
                        showSolution(
                            '访问方式错误',
                            '您似乎直接打开了HTML文件（使用file://协议），而不是通过HTTP服务器访问。<br><br>' +
                            '请按照以下步骤操作：<br>' +
                            '1. 双击项目目录中的 start-server.bat 文件启动服务器<br>' +
                            '2. 在浏览器中访问 http://localhost:8009/vue-check.html<br><br>' +
                            '直接打开HTML文件会因为浏览器安全策略导致文件无法加载。'
                        );
                        return;
                    } else {
                        addResult(`❌ 检查 ${file.name} 时出错: ${e.message}`, 'error');
                    }
                }
            });
            
            if (allExist) {
                addResult('✅ 所有文件都存在，可以继续下一步', 'success');
                document.getElementById('step2').disabled = false;
            } else if (missingFiles.length > 0) {
                addResult(`❌ 缺少文件: ${missingFiles.join(', ')}`, 'error');
                showSolution(
                    '文件缺失问题',
                    '请确保项目目录中包含所有必要的文件。特别是 js/vue.global.js 和 js/vue-app.js 文件。' +
                    '您可以尝试重新下载项目或从备份中恢复这些文件。'
                );
            }
            
            document.getElementById('step1').textContent = '步骤1: 检查Vue.js文件是否存在';
        }
        
        // 步骤2: 手动加载Vue.js
        function loadVueManually() {
            document.getElementById('step2').innerHTML = '<span class="loading"></span>加载中...';
            document.getElementById('step2').disabled = true;
            
            // 创建script标签手动加载Vue.js
            const script = document.createElement('script');
            script.src = 'js/vue.global.js';
            script.onload = function() {
                vueLoaded = true;
                addResult('✅ Vue.js 成功加载', 'success');
                addResult(`Vue版本: ${Vue ? Vue.version : '未知'}`, 'success');
                document.getElementById('step3').disabled = false;
                document.getElementById('step2').textContent = '步骤2: 手动加载Vue.js';
            };
            
            script.onerror = function() {
                vueLoaded = false;
                addResult('❌ Vue.js 加载失败', 'error');
                showSolution(
                    'Vue.js加载失败',
                    '可能是文件损坏或路径错误。请尝试以下解决方案：<br>' +
                    '1. 检查 js/vue.global.js 文件是否完整<br>' +
                    '2. 确保文件权限正确<br>' +
                    '3. 清除浏览器缓存后重试<br>' +
                    '4. 重新下载Vue.js文件'
                );
                document.getElementById('step2').textContent = '步骤2: 手动加载Vue.js';
            };
            
            document.head.appendChild(script);
        }
        
        // 步骤3: 检查Vue API
        function checkVueAPI() {
            document.getElementById('step3').innerHTML = '<span class="loading"></span>检查中...';
            document.getElementById('step3').disabled = true;
            
            if (!vueLoaded || !Vue) {
                addResult('❌ Vue.js 未加载，无法检查API', 'error');
                document.getElementById('step3').textContent = '步骤3: 检查Vue API';
                return;
            }
            
            const requiredAPIs = ['createApp', 'ref', 'reactive', 'computed', 'onMounted', 'onUnmounted'];
            let allAPIsPresent = true;
            
            requiredAPIs.forEach(api => {
                if (typeof Vue[api] === 'undefined') {
                    addResult(`❌ 缺少API: ${api}`, 'error');
                    allAPIsPresent = false;
                } else {
                    addResult(`✅ API存在: ${api}`, 'success');
                }
            });
            
            if (allAPIsPresent) {
                addResult('✅ 所有必需的Vue API都存在', 'success');
                document.getElementById('step4').disabled = false;
            } else {
                addResult('❌ 缺少必要的Vue API', 'error');
                showSolution(
                    'Vue API不完整',
                    'Vue.js库可能不完整或版本不兼容。请尝试：<br>' +
                    '1. 下载最新版本的Vue 3<br>' +
                    '2. 确保使用的是完整版本而非运行时版本<br>' +
                    '3. 检查文件是否完全下载无损坏'
                );
            }
            
            document.getElementById('step3').textContent = '步骤3: 检查Vue API';
        }
        
        // 步骤4: 初始化Vue应用
        function initVueApp() {
            document.getElementById('step4').innerHTML = '<span class="loading"></span>初始化中...';
            document.getElementById('step4').disabled = true;
            
            try {
                // 创建一个简单的Vue应用来测试
                const { createApp, ref } = Vue;
                
                const testApp = createApp({
                    setup() {
                        const message = ref('Vue应用初始化成功!');
                        return { message };
                    },
                    template: `<div style="padding: 20px; background: #d4edda; border-radius: 5px;">
                        <h3>{{ message }}</h3>
                        <p>Vue.js版本: {{ Vue.version }}</p>
                    </div>`
                });
                
                // 创建挂载点
                const mountPoint = document.createElement('div');
                mountPoint.id = 'test-app';
                document.getElementById('results').appendChild(mountPoint);
                
                testApp.mount('#test-app');
                addResult('✅ Vue应用初始化成功', 'success');
                addResult('您可以安全地使用Vue版本的应用了', 'success');
                
                // 提供跳转到主应用的按钮
                const button = document.createElement('button');
                button.textContent = '前往Vue版本应用';
                button.onclick = () => window.location.href = 'index.html';
                document.getElementById('results').appendChild(button);
                
            } catch (error) {
                addResult(`❌ Vue应用初始化失败: ${error.message}`, 'error');
                showSolution(
                    'Vue应用初始化失败',
                    '可能存在以下问题：<br>' +
                    '1. Vue.js版本不兼容<br>' +
                    '2. vue-app.js文件存在问题<br>' +
                    '3. 浏览器兼容性问题<br><br>' +
                    '建议使用原生JS版本以确保功能正常。'
                );
            }
            
            document.getElementById('step4').textContent = '步骤4: 初始化Vue应用';
        }
    </script>
</body>
</html>