<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 最小化测试</title>
    <script src="js/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vue.js 最小化测试</h1>
        <p>这个页面用于测试Vue.js是否能正常加载和运行。</p>
        
        <div id="app">
            <div v-if="vueLoaded" class="test-result success">
                <h2>Vue.js 加载成功!</h2>
                <p>Vue版本: {{ vueVersion }}</p>
                <p>当前计数: {{ count }}</p>
                <button @click="increment">增加计数</button>
            </div>
            <div v-else class="test-result error">
                <h2>Vue.js 加载失败</h2>
                <p>错误信息: {{ errorMessage }}</p>
            </div>
        </div>
    </div>

    <script>
        let vueLoaded = false;
        let errorMessage = '';
        let vueVersion = '未知';

        // 检查Vue是否加载
        try {
            if (typeof Vue === 'undefined') {
                throw new Error('Vue对象未定义');
            }
            
            vueVersion = Vue.version || '未知';
            
            if (!vueVersion.startsWith('3.')) {
                throw new Error(`Vue版本不兼容: ${vueVersion}，需要Vue 3.x`);
            }
            
            vueLoaded = true;
            console.log('Vue.js 加载成功，版本:', vueVersion);
        } catch (error) {
            vueLoaded = false;
            errorMessage = error.message;
            console.error('Vue.js 加载失败:', error);
        }

        // 如果Vue加载成功，创建应用
        if (vueLoaded) {
            const { createApp, ref } = Vue;
            
            createApp({
                setup() {
                    const count = ref(0);
                    const increment = () => {
                        count.value++;
                    };
                    
                    return {
                        vueLoaded: true,
                        vueVersion,
                        count,
                        increment
                    };
                }
            }).mount('#app');
        } else {
            // 如果Vue加载失败，显示错误信息
            document.getElementById('app').innerHTML = `
                <div class="test-result error">
                    <h2>Vue.js 加载失败</h2>
                    <p>错误信息: ${errorMessage}</p>
                    <h3>可能的解决方案:</h3>
                    <ol>
                        <li><strong>确保通过HTTP服务器访问</strong> - 不要直接打开HTML文件</li>
                        <li>检查 js/vue.global.js 文件是否存在且完整</li>
                        <li>尝试清除浏览器缓存后重新加载</li>
                        <li>使用原生JS版本: <a href="index-no-vue.html">index-no-vue.html</a></li>
                    </ol>
                    <button onclick="window.location.reload()">重新加载</button>
                    <button onclick="window.location.href='./'">返回主页</button>
                </div>
            `;
        }
    </script>
</body>
</html>