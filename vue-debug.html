<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 调试</title>
</head>
<body>
    <h1>Vue.js 调试页面</h1>
    <div id="debug-info"></div>
    <div id="app"></div>

    <script>
        const debugInfo = document.getElementById('debug-info');

        function log(message) {
            console.log(message);
            debugInfo.innerHTML += '<p>' + message + '</p>';
        }

        log('开始调试...');
        log('正在加载Vue.js...');

        // 动态加载Vue.js并检查
        const script = document.createElement('script');
        script.src = 'js/vue.global.js';
        script.onload = function() {
            log('Vue.js 脚本加载完成');
            log('typeof Vue: ' + typeof Vue);
            log('typeof window.Vue: ' + typeof window.Vue);

            if (typeof Vue !== 'undefined') {
                log('Vue 对象存在');
                log('Vue.version: ' + (Vue.version || '未知'));
                log('Vue.createApp: ' + typeof Vue.createApp);

                if (Vue.createApp) {
                    try {
                        const app = Vue.createApp({
                            data() {
                                return {
                                    message: 'Hello Vue!'
                                }
                            },
                            template: '<div>{{ message }}</div>'
                        });
                        app.mount('#app');
                        log('Vue 应用挂载成功');
                    } catch (error) {
                        log('Vue 应用挂载失败: ' + error.message);
                    }
                }
            } else {
                log('Vue 对象不存在');
                log('window.Vue: ' + typeof window.Vue);
                log('全局对象检查: ' + Object.keys(window).filter(key => key.toLowerCase().includes('vue')));
            }
        };

        script.onerror = function() {
            log('Vue.js 脚本加载失败');
        };

        document.head.appendChild(script);
    </script>
</body>
</html>
