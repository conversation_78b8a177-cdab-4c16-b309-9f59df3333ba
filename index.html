<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Icons -->
    <link rel="icon" href="assets/icon-192x192.png" type="image/png">
    <link rel="apple-touch-icon" href="assets/icon-192x192.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <style>
        .vue-fallback {
            display: none;
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            margin: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .vue-fallback h2 {
            color: #ff6b9d;
            margin-bottom: 15px;
        }
        
        .vue-fallback p {
            margin-bottom: 20px;
            color: #333;
        }
        
        .vue-fallback button {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .vue-fallback button:hover {
            background: #ff5a8f;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .vue-fallback button:active {
            transform: translateY(0);
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
            font-size: 14px;
            color: #666;
        }
        
        .error-details summary {
            cursor: pointer;
            color: #ff6b9d;
            font-weight: bold;
        }
        
        .diagnosis-link {
            display: inline-block;
            margin-top: 15px;
            color: #ff6b9d;
            text-decoration: none;
            font-weight: bold;
        }
        
        .diagnosis-link:hover {
            text-decoration: underline;
        }
        
        .important-note {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
            text-align: left;
        }
        
        .loading-container {
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Vue应用容器 -->
    <div id="app">
        <!-- Vue应用内容将在这里渲染 -->
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载心动告白应用...</p>
            <div class="important-note">
                <h3>重要提示</h3>
                <p>如果您看到此页面，说明您可能直接打开了HTML文件。</p>
                <p>请通过HTTP服务器访问此应用以确保所有功能正常工作：</p>
                <p>1. 双击项目目录中的 <strong>start-server.bat</strong> 文件启动服务器</p>
                <p>2. 在浏览器中访问 <strong>http://localhost:8009</strong></p>
            </div>
            <p style="font-size: 14px; margin-top: 10px;">
                如果长时间未加载，请<a href="#" onclick="showFallback()">点击这里</a>
            </p>
        </div>
    </div>
    
    <!-- Vue.js 3 (使用本地文件) -->
    <script src="js/vue.global.js"></script>
    
    <!-- 应用脚本 -->
    <script src="js/vue-app.js"></script>
    
    <!-- Vue加载失败的后备方案 -->
    <script>
        // 检查Vue是否加载成功
        function checkVueLoaded() {
            // 检查Vue对象是否存在
            if (typeof Vue === 'undefined') {
                console.error('Vue.js 未加载');
                showFallback('Vue.js库文件未加载，请检查网络连接或文件路径。');
                return false;
            }
            
            // 检查Vue版本
            if (!Vue.version || !Vue.version.startsWith('3.')) {
                console.error('Vue版本不兼容:', Vue.version);
                showFallback('Vue版本不兼容，请使用Vue 3.x版本。');
                return false;
            }
            
            // 检查必要的API是否存在
            const requiredAPIs = ['createApp', 'ref', 'reactive', 'computed'];
            for (const api of requiredAPIs) {
                if (typeof Vue[api] === 'undefined') {
                    console.error('缺少必要的Vue API:', api);
                    showFallback(`缺少必要的Vue API: ${api}`);
                    return false;
                }
            }
            
            console.log('Vue.js 加载成功，版本:', Vue.version);
            return true;
        }
        
        // 显示后备方案
        function showFallback(errorDetails = '') {
            // 隐藏loading
            const loading = document.querySelector('.loading-container');
            if (loading) {
                loading.style.display = 'none';
            }
            
            // 显示后备方案
            const fallback = document.createElement('div');
            fallback.className = 'vue-fallback';
            fallback.innerHTML = `
                <h2>Vue.js 加载失败</h2>
                <div class="important-note">
                    <h3>重要提示</h3>
                    <p>请确保您是通过HTTP服务器访问此页面，而不是直接打开HTML文件。</p>
                    <p>直接打开HTML文件会导致浏览器安全策略阻止文件加载。</p>
                    <p>正确的方式是：</p>
                    <ol>
                        <li>运行 start-server.bat 文件启动服务器</li>
                        <li>在浏览器中访问 http://localhost:8009</li>
                    </ol>
                </div>
                <p>抱歉，Vue.js版本的应用加载失败了。</p>
                ${errorDetails ? `<div class="error-details">
                    <details>
                        <summary>错误详情</summary>
                        <p>${errorDetails}</p>
                    </details>
                </div>` : ''}
                <p>您可以选择以下方式继续：</p>
                <button onclick="window.location.href='index-no-vue.html'">使用原生JS版本（推荐）</button>
                <button onclick="window.location.reload()">重试加载</button>
                <button onclick="checkFiles()">检查文件</button>
                <a href="vue-check.html" class="diagnosis-link">运行详细诊断工具</a>
            `;
            document.getElementById('app').appendChild(fallback);
            fallback.style.display = 'block';
        }
        
        // 检查必要文件是否存在
        function checkFiles() {
            const files = [
                'js/vue.global.js',
                'js/vue-app.js',
                'css/style.css'
            ];
            
            let allExist = true;
            let missingFiles = [];
            
            files.forEach(file => {
                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', file, false);
                try {
                    xhr.send();
                    if (xhr.status >= 400) {
                        allExist = false;
                        missingFiles.push(file);
                    }
                } catch (e) {
                    allExist = false;
                    missingFiles.push(file);
                    // 检查是否是因为跨域问题导致的错误
                    if (e.message.includes('Failed to execute \'send\' on \'XMLHttpRequest\'')) {
                        alert('您似乎直接打开了HTML文件，请通过HTTP服务器访问此页面。\n\n请按照以下步骤操作：\n1. 双击项目目录中的 start-server.bat 文件启动服务器\n2. 在浏览器中访问 http://localhost:8009');
                        return;
                    }
                }
            });
            
            if (allExist) {
                alert('所有文件都存在，可能是其他问题导致加载失败。');
            } else {
                alert('以下文件缺失：\n' + missingFiles.join('\n'));
            }
        }
        
        // 页面加载完成后检查Vue是否加载成功
        window.addEventListener('load', function() {
            // 延迟检查，给Vue一些加载时间
            setTimeout(function() {
                if (!checkVueLoaded()) {
                    // 如果5秒后Vue仍未加载成功，显示后备方案
                    setTimeout(function() {
                        const app = document.getElementById('app');
                        if (app && app.querySelector('.loading-container')) {
                            showFallback('加载超时，请检查网络连接或文件完整性。');
                        }
                    }, 5000);
                }
            }, 1000);
        });
    </script>
</body>
</html>