<!DOCTYPE html>
<html>
<head>
    <title>Final Vue Test</title>
</head>
<body>
    <h1>Final Vue Test</h1>
    <div id="status"></div>
    <div id="app"></div>
    
    <script>
        function log(msg) {
            console.log(msg);
            document.getElementById('status').innerHTML += '<p>' + msg + '</p>';
        }
        
        log('Starting final test...');
        
        // 直接在HTML中嵌入一个简化的Vue.js测试
        window.testVue = function() {
            // 创建一个最简单的Vue应用来测试
            if (typeof Vue !== 'undefined' && Vue.createApp) {
                try {
                    const app = Vue.createApp({
                        data() {
                            return {
                                message: 'Vue 3 is working!'
                            }
                        },
                        template: '<div>{{ message }}</div>'
                    });
                    app.mount('#app');
                    log('✅ Vue应用成功挂载！');
                    return true;
                } catch (error) {
                    log('❌ Vue应用挂载失败: ' + error.message);
                    return false;
                }
            } else {
                log('❌ Vue对象不可用');
                return false;
            }
        };
        
        // 加载Vue.js
        const script = document.createElement('script');
        script.src = 'js/vue.global.js';
        
        script.onload = function() {
            log('Vue.js脚本加载完成');
            
            // 等待一小段时间确保Vue对象被正确创建
            setTimeout(function() {
                log('检查Vue对象...');
                log('typeof Vue: ' + typeof Vue);
                log('typeof window.Vue: ' + typeof window.Vue);
                
                if (typeof Vue === 'undefined') {
                    log('尝试从window.Vue获取...');
                    if (typeof window.Vue !== 'undefined') {
                        window.Vue = window.Vue;
                        log('从window.Vue成功获取Vue对象');
                    }
                }
                
                // 测试Vue
                if (testVue()) {
                    log('🎉 Vue.js测试成功！');
                } else {
                    log('💥 Vue.js测试失败');
                    // 显示备用内容
                    document.getElementById('app').innerHTML = 
                        '<div style="color: red;">Vue.js加载失败，请使用原生JS版本</div>';
                }
            }, 100);
        };
        
        script.onerror = function() {
            log('❌ Vue.js脚本加载失败');
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
