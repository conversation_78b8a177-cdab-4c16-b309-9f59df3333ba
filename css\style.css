/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Loading container */
.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: white;
    font-size: 1.2rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast notification */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    z-index: 2000;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
}

.toast.success {
    background: rgba(46, 204, 113, 0.9);
}

.toast.error {
    background: rgba(231, 76, 60, 0.9);
}

.toast.warning {
    background: rgba(241, 196, 15, 0.9);
}

.toast.info {
    background: rgba(52, 152, 219, 0.9);
}

/* App container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 20px;
    max-width: 480px;
    margin: 0 auto;
}

/* Particles background */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    z-index: 10;
    position: relative;
}

.app-title {
    font-size: 2.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
    font-weight: bold;
}

.progress-indicator {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    display: inline-block;
}

.progress-indicator span {
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
}

/* Heart container */
.heart-container {
    position: relative;
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#heart-canvas {
    max-width: 280px;
    max-height: 280px;
    cursor: pointer;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    transition: transform 0.1s ease;
}

#heart-canvas:hover {
    transform: scale(1.05);
}

#heart-canvas:active {
    transform: scale(0.95);
}

.heart-prompt {
    margin-top: 15px;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: pulse 2s ease-in-out infinite;
}

.heart-prompt span {
    color: #c44569;
    font-weight: bold;
    font-size: 1.1rem;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Message container */
.message-container {
    width: 100%;
    max-width: 350px;
    margin-bottom: 30px;
}

.message-box {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.message-text {
    font-size: 1.2rem;
    color: #2d3436;
    line-height: 1.6;
    font-weight: 500;
    opacity: 0;
    animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 12px 16px;
    border-radius: 15px;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 70px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn span:first-child {
    font-size: 1.2rem;
}

/* Settings button */
.settings-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 20px;
    width: 100%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-content h2 {
    color: #c44569;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.message-input-group {
    margin-bottom: 15px;
}

.message-input-group label {
    display: block;
    margin-bottom: 5px;
    color: #2d3436;
    font-weight: 500;
}

.message-input-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.message-input-group input:focus {
    outline: none;
    border-color: #ff6b9d;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.btn-primary {
    background: #ff6b9d;
    color: white;
}

.btn-primary:hover {
    background: #e55a87;
}

.btn-secondary {
    background: #ddd;
    color: #2d3436;
}

.btn-secondary:hover {
    background: #ccc;
}

/* Toast notification */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 0.9rem;
    z-index: 10000;
    animation: toastSlideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.toast.success {
    background: rgba(76, 175, 80, 0.9);
}

.toast.error {
    background: rgba(244, 67, 54, 0.9);
}

.toast.warning {
    background: rgba(255, 152, 0, 0.9);
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Animations for heart explosion */
.heart-explode {
    animation: explode 0.6s ease-out forwards;
}

@keyframes explode {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Music visualization */
.music-active {
    animation: musicPulse 1s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.4) !important;
}

@keyframes musicPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Music loading state */
.music-loading {
    animation: musicLoading 1s linear infinite;
}

@keyframes musicLoading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 480px) {
    .app-container {
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    #heart-canvas {
        max-width: 240px;
        max-height: 240px;
    }
    
    .message-box {
        padding: 15px;
    }
    
    .message-text {
        font-size: 1.1rem;
    }
    
    .action-buttons {
        gap: 10px;
    }
    
    .action-btn {
        min-width: 60px;
        padding: 10px 12px;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .toast {
        font-size: 0.8rem;
        padding: 10px 20px;
        top: 15px;
    }
}

@media (max-width: 360px) {
    .app-title {
        font-size: 1.8rem;
    }

    #heart-canvas {
        max-width: 200px;
        max-height: 200px;
    }

    .message-text {
        font-size: 1rem;
    }

    .action-btn {
        font-size: 0.8rem;
        min-width: 50px;
    }
}

/* Screenshot flash effect */
.screenshot-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    animation: flash 0.3s ease-out;
}

@keyframes flash {
    0% { opacity: 0; }
    50% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 20px;
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content h2 {
    color: #c44569;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.message-input-group {
    margin-bottom: 15px;
}

.message-input-group label {
    display: block;
    margin-bottom: 5px;
    color: #2d3436;
    font-weight: 500;
}

.message-input-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.message-input-group input:focus {
    outline: none;
    border-color: #ff6b9d;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.btn-primary {
    background: #ff6b9d;
    color: white;
}

.btn-primary:hover {
    background: #e55a87;
}

.btn-secondary {
    background: #ddd;
    color: #2d3436;
}

.btn-secondary:hover {
    background: #ccc;
}

/* Enhanced heart explosion animation */
.heart-explode {
    animation: heartExplode 0.6s ease-out forwards;
}

@keyframes heartExplode {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.3);
        filter: brightness(1.5) saturate(1.5);
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

/* Tab navigation styles */
.tab-navigation {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    color: #ff6b9d;
    background: rgba(255, 107, 157, 0.1);
}

.tab-btn.active {
    color: #ff6b9d;
    border-bottom-color: #ff6b9d;
    background: rgba(255, 107, 157, 0.1);
}

/* Tab content styles */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

/* Music upload area styles */
.music-upload-area {
    margin-bottom: 25px;
}

.upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-zone:hover {
    border-color: #ff6b9d;
    background: rgba(255, 107, 157, 0.05);
}

.upload-zone.dragover {
    border-color: #ff6b9d;
    background: rgba(255, 107, 157, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #6c757d;
}

.upload-text p {
    margin: 5px 0;
    color: #6c757d;
}

.upload-text p:first-child {
    color: #495057;
    font-size: 1.1rem;
}

/* Music list styles */
.music-list h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.music-items {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
}

.music-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    transition: background 0.3s ease;
}

.music-item:last-child {
    border-bottom: none;
}

.music-item:hover {
    background: rgba(255, 107, 157, 0.05);
}

.music-item.selected {
    background: rgba(255, 107, 157, 0.1);
    border-left: 4px solid #ff6b9d;
}

.music-info {
    flex: 1;
    margin-left: 15px;
}

.music-name {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.music-details {
    font-size: 0.9rem;
    color: #6c757d;
}

.music-controls {
    display: flex;
    gap: 8px;
}

.music-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.music-btn.preview {
    background: #17a2b8;
    color: white;
}

.music-btn.preview:hover {
    background: #138496;
}

.music-btn.select {
    background: #28a745;
    color: white;
}

.music-btn.select:hover {
    background: #218838;
}

.music-btn.delete {
    background: #dc3545;
    color: white;
}

.music-btn.delete:hover {
    background: #c82333;
}

.music-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Music icon styles */
.music-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

/* Upload progress styles */
.upload-progress {
    margin-top: 15px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 5px;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d, #c44569);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 8px;
    font-size: 0.9rem;
    color: #6c757d;
    text-align: center;
}
